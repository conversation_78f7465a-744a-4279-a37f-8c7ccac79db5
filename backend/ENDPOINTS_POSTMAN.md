# Endpoints para Postman - Sistema de Reclamos

**Base URL:** `http://localhost:3000/api/v1`

## 🔐 Autenticación

### Login
- **POST** `/auth/login`
- **Body (JSON):**
```json
{
  "correoElectronico": "<EMAIL>",
  "contrasenia": "password123"
}
```
- **Response:** Token JWT para usar en Authorization Header

---

## 👥 Clientes (Usuarios tipo 3)

### Crear Cliente
- **POST** `/clientes/crear`
- **Headers:** `Content-Type: multipart/form-data`
- **Body (form-data):**
  - `nombre`: string
  - `apellido`: string
  - `correoElectronico`: string
  - `contrasenia`: string
  - `imagen`: file (opcional)

### Actualizar Cliente
- **PATCH** `/clientes/actualizar`
- **Headers:** 
  - `Authorization: Bearer <token>`
  - `Content-Type: multipart/form-data`
- **Body (form-data):**
  - `nombre`: string (opcional)
  - `apellido`: string (opcional)
  - `correoElectronico`: string (opcional)
  - `contrasenia`: string (opcional)
  - `imagen`: file (opcional)

---

## 🛠️ Administradores (Usuarios tipo 1)

### Obtener Todos los Empleados
- **GET** `/admins/empleados`
- **Headers:** `Authorization: Bearer <token>`

### Obtener Todos los Clientes
- **GET** `/admins/clientes`
- **Headers:** `Authorization: Bearer <token>`

### Crear Usuario
- **POST** `/admins/usuario`
- **Headers:** 
  - `Authorization: Bearer <token>`
  - `Content-Type: multipart/form-data`
- **Body (form-data):**
  - `nombre`: string
  - `apellido`: string
  - `correoElectronico`: string
  - `contrasenia`: string
  - `idTipoUsuario`: number (1=Admin, 2=Empleado, 3=Cliente)
  - `imagen`: file (opcional)

### Actualizar Usuario
- **PATCH** `/admins/actualizar-usuario/:idUsuarioModificado`
- **Headers:** 
  - `Authorization: Bearer <token>`
  - `Content-Type: multipart/form-data`
- **Body (form-data):**
  - `nombre`: string (opcional)
  - `apellido`: string (opcional)
  - `correoElectronico`: string (opcional)
  - `contrasenia`: string (opcional)
  - `idTipoUsuario`: number (opcional)
  - `imagen`: file (opcional)

### Borrar Usuario
- **PUT** `/admins/borrar-usuario/:idUsuario`
- **Headers:** `Authorization: Bearer <token>`

---

## 🏢 Oficinas (Solo Administradores)

### Obtener Todas las Oficinas
- **GET** `/oficinas/obtener`
- **Headers:** `Authorization: Bearer <token>`

### Obtener Empleados por Oficina
- **GET** `/oficinas/:idOficina/empleados`
- **Headers:** `Authorization: Bearer <token>`

### Asignar Empleado a Oficina
- **POST** `/oficinas/:idOficina/empleado/:idUsuario`
- **Headers:** `Authorization: Bearer <token>`

### Eliminar Empleado de Oficina
- **PUT** `/oficinas/empleado/:idUsuario`
- **Headers:** `Authorization: Bearer <token>`

---

## 📝 Reclamos (Clientes - Usuarios tipo 3)

### Obtener Estados de Reclamo
- **GET** `/reclamos/estado`
- **Headers:** `Authorization: Bearer <token>`

### Crear Reclamo
- **POST** `/reclamos/crear`
- **Headers:** `Authorization: Bearer <token>`
- **Body (JSON):**
```json
{
  "asunto": "Título del reclamo",
  "descripcion": "Descripción detallada del reclamo",
  "idReclamoTipo": 1
}
```

### Cancelar Reclamo
- **PUT** `/reclamos/:idReclamo/cancelar`
- **Headers:** `Authorization: Bearer <token>`

---

## 🏢 Reclamos de Oficina (Empleados - Usuarios tipo 2)

### Listar Reclamos de la Oficina
- **GET** `/reclamoOficinas/listar`
- **Headers:** `Authorization: Bearer <token>`

### Actualizar Estado de Reclamo
- **PUT** `/reclamoOficinas/cliente/:idCliente/reclamo/:idReclamo/estado/:nuevoEstado`
- **Headers:** `Authorization: Bearer <token>`

---

## 🏷️ Tipos de Reclamo (Solo Administradores)

### Obtener Todos los Tipos de Reclamo
- **GET** `/reclamoTipos/obtener`
- **Headers:** `Authorization: Bearer <token>`

### Crear Tipo de Reclamo
- **POST** `/reclamoTipos/crear`
- **Headers:** `Authorization: Bearer <token>`
- **Body (JSON):**
```json
{
  "descripcion": "Nuevo tipo de reclamo"
}
```

### Actualizar Tipo de Reclamo
- **PATCH** `/reclamoTipos/actualizar/:idReclamoTipo`
- **Headers:** `Authorization: Bearer <token>`
- **Body (JSON):**
```json
{
  "descripcion": "Descripción actualizada"
}
```

### Borrar Tipo de Reclamo
- **PUT** `/reclamoTipos/borrar/:idReclamoTipo`
- **Headers:** `Authorization: Bearer <token>`

---

## 📊 Estadísticas (Solo Administradores)

### Obtener Estadísticas Completas
- **GET** `/estadisticas/obtener`
- **Headers:** `Authorization: Bearer <token>`
- **Response:** Estadísticas completas de reclamos

---

## 📄 Informes (Solo Administradores)

### Generar Informe PDF
- **GET** `/informes/informe?formato=pdf`
- **Headers:** `Authorization: Bearer <token>`
- **Response:** Archivo PDF

### Generar Informe CSV
- **GET** `/informes/informe?formato=csv`
- **Headers:** `Authorization: Bearer <token>`
- **Response:** Archivo CSV

---

## 📁 Archivos Estáticos

### Imágenes de Usuario
- **GET** `/uploads/:nombreArchivo`
- **Descripción:** Acceso directo a imágenes subidas

---

## 🔑 Tipos de Usuario

1. **Administrador (tipo 1):** Acceso completo al sistema
2. **Empleado (tipo 2):** Gestión de reclamos de su oficina
3. **Cliente (tipo 3):** Creación y seguimiento de sus reclamos

---

## 📋 Notas Importantes

1. **Autenticación:** Todos los endpoints excepto `/auth/login` y `/clientes/crear` requieren token JWT
2. **Authorization Header:** `Bearer <tu_token_jwt>`
3. **Archivos:** Para subir imágenes usar `multipart/form-data`
4. **Errores:** El sistema devuelve códigos HTTP estándar (200, 400, 401, 403, 500)
5. **CORS:** Configurado para desarrollo local
